import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from './ApiService';
import DatabaseIntegrationService from './DatabaseIntegrationService';

export interface CachedRecipe {
  id: string;
  title: string;
  description: string;
  ingredients: string[];
  instructions: string[];
  nutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  cookTime: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  servings: number;
  tags: string[];
  imageUrl: string;
  source: 'weekly_plan' | 'generated' | 'manual';
  cachedAt: number;
  lastAccessed: number;
}

class RecipeCacheService {
  private static instance: RecipeCacheService;
  private readonly RECIPE_CACHE_KEY = 'cached_recipes';
  private readonly CACHE_EXPIRY_DAYS = 30; // Recipes expire after 30 days
  private readonly MAX_CACHED_RECIPES = 100; // Maximum recipes to keep in cache

  static getInstance(): RecipeCacheService {
    if (!RecipeCacheService.instance) {
      RecipeCacheService.instance = new RecipeCacheService();
    }
    return RecipeCacheService.instance;
  }

  // Generate a consistent recipe ID based on meal name
  private generateRecipeId(mealName: string): string {
    return `recipe_${mealName.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;
  }

  // Check if recipe exists in cache
  async isCached(mealName: string): Promise<boolean> {
    try {
      const recipeId = this.generateRecipeId(mealName);
      const cachedRecipes = await this.getCachedRecipes();
      const recipe = cachedRecipes.find(r => r.id === recipeId);
      
      if (!recipe) return false;
      
      // Check if recipe is expired
      const isExpired = this.isRecipeExpired(recipe);
      if (isExpired) {
        await this.removeFromCache(recipeId);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('❌ Error checking recipe cache:', error);
      return false;
    }
  }

  // Get recipe from cache
  async getCachedRecipe(mealName: string): Promise<CachedRecipe | null> {
    try {
      const recipeId = this.generateRecipeId(mealName);
      const cachedRecipes = await this.getCachedRecipes();
      const recipe = cachedRecipes.find(r => r.id === recipeId);
      
      if (!recipe) return null;
      
      // Check if recipe is expired
      if (this.isRecipeExpired(recipe)) {
        await this.removeFromCache(recipeId);
        return null;
      }
      
      // Update last accessed time
      recipe.lastAccessed = Date.now();
      await this.updateRecipeInCache(recipe);
      
      console.log(`📖 Retrieved cached recipe: ${mealName}`);
      return recipe;
    } catch (error) {
      console.error('❌ Error getting cached recipe:', error);
      return null;
    }
  }

  // Generate and cache new recipe
  async generateAndCacheRecipe(mealName: string, source: 'weekly_plan' | 'generated' = 'generated'): Promise<CachedRecipe> {
    try {
      console.log(`🔄 Generating new recipe for: ${mealName}`);
      
      // Generate recipe using API
      const apiRecipe = await ApiService.generateRecipe(mealName);
      
      // Convert to cached recipe format
      const cachedRecipe: CachedRecipe = {
        id: this.generateRecipeId(mealName),
        title: apiRecipe.recipeTitle,
        description: `Delicious ${mealName.toLowerCase()} recipe with ${apiRecipe.estimatedCalories} calories`,
        ingredients: apiRecipe.ingredients,
        instructions: apiRecipe.steps,
        nutrition: {
          calories: apiRecipe.estimatedCalories,
          protein: parseFloat(apiRecipe.macros.protein.replace('g', '')) || 25,
          carbs: parseFloat(apiRecipe.macros.carbs.replace('g', '')) || 30,
          fat: parseFloat(apiRecipe.macros.fats.replace('g', '')) || 15,
        },
        cookTime: '30 min',
        difficulty: 'Medium',
        servings: 4,
        tags: apiRecipe.tags,
        imageUrl: apiRecipe.imageUrl || 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800&h=600&fit=crop&crop=center&q=80',
        source,
        cachedAt: Date.now(),
        lastAccessed: Date.now()
      };
      
      // Cache the recipe
      await this.addToCache(cachedRecipe);
      
      // Also save to database for backup
      try {
        await DatabaseIntegrationService.syncRecipeToDatabase({
          title: cachedRecipe.title,
          ingredients: cachedRecipe.ingredients,
          instructions: cachedRecipe.instructions,
          nutrition: cachedRecipe.nutrition,
          tags: cachedRecipe.tags,
          imageUrl: cachedRecipe.imageUrl,
          cookTime: cachedRecipe.cookTime,
          difficulty: cachedRecipe.difficulty,
          servings: cachedRecipe.servings,
          isFavorite: false
        });
      } catch (dbError) {
        console.warn('⚠️ Failed to save recipe to database:', dbError);
      }
      
      console.log(`✅ Generated and cached recipe: ${mealName}`);
      return cachedRecipe;
    } catch (error) {
      console.error('❌ Error generating recipe:', error);
      throw error;
    }
  }

  // Get or generate recipe (main method)
  async getOrGenerateRecipe(mealName: string, source: 'weekly_plan' | 'generated' = 'generated'): Promise<CachedRecipe> {
    try {
      // First check cache
      const cachedRecipe = await this.getCachedRecipe(mealName);
      if (cachedRecipe) {
        console.log(`📖 Using cached recipe for: ${mealName}`);
        return cachedRecipe;
      }
      
      // Generate new recipe if not cached
      console.log(`🔄 Recipe not cached, generating: ${mealName}`);
      return await this.generateAndCacheRecipe(mealName, source);
    } catch (error) {
      console.error('❌ Error in getOrGenerateRecipe:', error);
      throw error;
    }
  }

  // Private helper methods
  private async getCachedRecipes(): Promise<CachedRecipe[]> {
    try {
      const cached = await AsyncStorage.getItem(this.RECIPE_CACHE_KEY);
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      console.error('❌ Error getting cached recipes:', error);
      return [];
    }
  }

  private async saveCachedRecipes(recipes: CachedRecipe[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.RECIPE_CACHE_KEY, JSON.stringify(recipes));
    } catch (error) {
      console.error('❌ Error saving cached recipes:', error);
    }
  }

  private async addToCache(recipe: CachedRecipe): Promise<void> {
    try {
      const recipes = await this.getCachedRecipes();
      
      // Remove existing recipe with same ID
      const filteredRecipes = recipes.filter(r => r.id !== recipe.id);
      
      // Add new recipe
      filteredRecipes.unshift(recipe);
      
      // Limit cache size
      const limitedRecipes = filteredRecipes.slice(0, this.MAX_CACHED_RECIPES);
      
      await this.saveCachedRecipes(limitedRecipes);
    } catch (error) {
      console.error('❌ Error adding recipe to cache:', error);
    }
  }

  private async updateRecipeInCache(updatedRecipe: CachedRecipe): Promise<void> {
    try {
      const recipes = await this.getCachedRecipes();
      const index = recipes.findIndex(r => r.id === updatedRecipe.id);
      
      if (index !== -1) {
        recipes[index] = updatedRecipe;
        await this.saveCachedRecipes(recipes);
      }
    } catch (error) {
      console.error('❌ Error updating recipe in cache:', error);
    }
  }

  private async removeFromCache(recipeId: string): Promise<void> {
    try {
      const recipes = await this.getCachedRecipes();
      const filteredRecipes = recipes.filter(r => r.id !== recipeId);
      await this.saveCachedRecipes(filteredRecipes);
    } catch (error) {
      console.error('❌ Error removing recipe from cache:', error);
    }
  }

  private isRecipeExpired(recipe: CachedRecipe): boolean {
    const expiryTime = recipe.cachedAt + (this.CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
    return Date.now() > expiryTime;
  }

  // Cleanup expired recipes
  async cleanupExpiredRecipes(): Promise<void> {
    try {
      const recipes = await this.getCachedRecipes();
      const validRecipes = recipes.filter(recipe => !this.isRecipeExpired(recipe));
      
      if (validRecipes.length !== recipes.length) {
        await this.saveCachedRecipes(validRecipes);
        console.log(`🧹 Cleaned up ${recipes.length - validRecipes.length} expired recipes`);
      }
    } catch (error) {
      console.error('❌ Error cleaning up expired recipes:', error);
    }
  }

  // Get cache statistics
  async getCacheStats(): Promise<{ totalRecipes: number; cacheSize: string }> {
    try {
      const recipes = await this.getCachedRecipes();
      const cacheData = await AsyncStorage.getItem(this.RECIPE_CACHE_KEY);
      const sizeInBytes = cacheData ? new Blob([cacheData]).size : 0;
      const sizeInKB = (sizeInBytes / 1024).toFixed(2);

      return {
        totalRecipes: recipes.length,
        cacheSize: `${sizeInKB} KB`
      };
    } catch (error) {
      console.error('❌ Error getting cache stats:', error);
      return { totalRecipes: 0, cacheSize: '0 KB' };
    }
  }

  // Get all cached recipes (for debugging/admin purposes)
  async getAllCachedRecipes(): Promise<CachedRecipe[]> {
    return await this.getCachedRecipes();
  }

  // Clear all cached recipes
  async clearAllCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.RECIPE_CACHE_KEY);
      console.log('🧹 All recipe cache cleared');
    } catch (error) {
      console.error('❌ Error clearing recipe cache:', error);
    }
  }

  // Get recipes by source
  async getRecipesBySource(source: 'weekly_plan' | 'generated'): Promise<CachedRecipe[]> {
    try {
      const recipes = await this.getCachedRecipes();
      return recipes.filter(recipe => recipe.source === source);
    } catch (error) {
      console.error('❌ Error getting recipes by source:', error);
      return [];
    }
  }
}

export default RecipeCacheService.getInstance();
